
from typing import List, Dict, Type, TypedDict, Any
import json
from langgraph.graph import StateGraph, END
from langchain_openai import ChatOpenAI
from langchain.schema import SystemMessage, HumanMessage
from langchain_core.messages import ToolMessage
import os
from src.python_executor2 import execute_python_code, PYTHON_EXECUTOR_TOOL
import tempfile


def make_python_node(code_template, state_key):
    def node(state: Dict[str, Any]) -> Dict[str, Any]:
        # Fill the code template with state variables
        filled_code = code_template.format(**state)

        # Execute Python directly
        execution_result = execute_python_code(filled_code, env=tool_env)

        # Return the variables as the step result
        return {state_key: execution_result['variables']}

    return node
metadata:
  owner: "Damjan"
  markdown_db_table: "wget2_runs"

prompts:
  - name: find_team_section
    prompt: |    
      For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
      
      You are given a company's website in markdown.
      Identify sections that list employees. Look for headings like "Team", "Our People", "Management", "Leadership", "Staff", "Board", or similar. 
      Return only the section text if found. If no team page exists, return exactly "no_team_page". 
      
      <markdown>{markdown}</markdown>
  
      Respond with a JSON object like:
      {{
        "team_section": ...,   // string, the team section text. "no_team_page" if no team page exists.
      }}
      
      But don't preface it with ```json.

  - name: extract_team_section
    prompt: |
      For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
  
      You are given a section of a company's website that lists employees:
      
      input: {find_team_section[team_section]}
  
      You have 2 tasks:  
      (1) Decide if this section represents:
      - all employees of the company ("full")
      - only a subset, such as executives or a department ("partial")
      You can assume full if there is more than 10 people mentioned, and preferably have multiple categories / roles, not just management.
      
      (2) Extract a list of all individual team member names mentioned. Ignore headings, job titles, and company names. Only return real person names.
      The names should be extracted from your reasoning, not by executing Python code!
      
      Respond with a JSON object like:
      {{
        "team_section_complete": "full" or "partial"
        "employee_names": [...]   // list of strings, the names of the employees, or empty list if no names are found
      }}
  
      But don't preface it with ```json. Ensure the output is valid JSON.

  - name: count_employees
    type: "python"
    prompt: |
      # Get the team section from previous step, """ needed for multi-line text
      team_section = """{find_team_section[team_section]}"""
      
      # Simple variables
      char_count = len(team_section)
      status = "OK"
      employee_count = 19  # We know there are 19 from the LLM step
      employee_names = ["Anke Ehmer", "Annett Dietrich"]  # Just a few for testing
      
      print(f"Character count: {char_count}")
      print(f"Status: {status}")
      print(f"Employee count: {employee_count}")

      #employees_list = "{extract_team_section[employee_names]}"

      # Check if input is too short
      #char_count = len(team_section)
      #print("Character count: " + str(char_count))

      # Debug: Print what we have so far
      #print("team_section length:" + str(len(team_section)))
      #print("char_count variable:" + char_count)

      #if char_count < 50:
      #    status = "Too short to be a team section"
      #    employee_names = []
      #    employee_count = 0
      #    print("Status: Too short to be a team section")
      #else:
      #    employee_count = len(employees_list)
      #    employee_names = employees_list
      #    status = "OK"
      #    print("Employee count: " + employee_count)
      #    print("Status: OK")

      #print("Found names: " + str(employee_names))
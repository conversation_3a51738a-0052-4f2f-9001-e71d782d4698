from langgraph.graph import StateGraph, E<PERSON>
from langchain_openai import ChatOpenAI
import os
from src.nodes.llm_node import make_node as make_llm_node
from src.nodes.python_node import make_python_node

# System message for LLM with tool calling capability
SYSTEM_MESSAGE = """You are a helpful assistant that can execute Python code when needed for calculations, data processing, and analysis.

You have access to a Python interpreter tool that you can use for:
- Mathematical calculations (averages, ranges, etc.)
- Counting and list operations
- Data processing and analysis
- Any computational work that would be easier with code
When calling tools, the argument key must be exactly "code" (not code=).

IMPORTANT — Big input handling:
- Large inputs (e.g., full markdown) are provided via a helper function `read_blob_text()` inside the Python tool runtime.
- NEVER paste large input directly into your Python source string.
- When you need the full markdown, call:
    text = read_blob_text()
    
When you need to perform calculations or data processing, use the execute_python_code tool. Available Python modules include: math, datetime, re, json.
Be careful because sometimes you will be asked to NOT use the tool and instead use your reasoning.

Always respond with valid JSON only at the end of your response."""

# SYSTEM_MESSAGE = """
#  You are a helpful assistent who extracts information from some provided prompts that include markdowns.
#  Always respond with valid <PERSON><PERSON><PERSON> only at the end of your response. No need to use reasoning, just extract the information, it should be straightforward.
#  Always reply in English no matter what language the input is.
#  The information you extract should be based and contained within the provided markdown! Do not make things up, better to say so when you don't have the information.
# """


def make_node(llm, prompt, state_key, step_type="llm"):
    if step_type == "python":
        return make_python_node(prompt, state_key)
    else:
        return make_llm_node(llm, prompt, state_key)

class LangGraphUtils:
    def __init__(self, model="gpt-4o-mini"):
        if model.lower().startswith("gpt"):
            self.llm = ChatOpenAI(model=model, #temperature=0.0,
                                  api_key=os.getenv("OPEN_AI_API_KEY"),
                                  timeout=60, max_retries=2) #, max_tokens=125_000)


    def build_graph(self, prompts, workflow):
        """Build a LangGraph from a list of prompts"""

        # Create a state graph with our dynamic state type
        sg = StateGraph(workflow)

        # Create a node for each prompt
        for step in prompts:
            step_name = step['name']
            step_type = step.get('type', 'llm')
            prompt_template = step['prompt']
            node_name = f"{step_name}_node ({step_type})"

            sg.add_node(node_name, make_node(self.llm, prompt_template, step_name))

        # Create edges between nodes
        for i in range(len(prompts) - 1):
            current_node = f"{prompts[i]['name']}_node"
            next_node = f"{prompts[i + 1]['name']}_node"
            sg.add_edge(current_node, next_node)

        # Set entry point and final edge
        first_node = f"{prompts[0]['name']}_node"
        last_node = f"{prompts[-1]['name']}_node"
        sg.add_edge(last_node, END)
        sg.set_entry_point(first_node)

        # Compile the graph
        return sg.compile()




from typing import List, Dict, Type, TypedDict, Any
import json
from langgraph.graph import StateGraph, END
from langchain_openai import ChatOpenAI
from langchain.schema import SystemMessage, HumanMessage
from langchain_core.messages import ToolMessage
import os
from src.python_executor2 import execute_python_code, PYTHON_EXECUTOR_TOOL
import tempfile
from src.nodes.msgs import SYSTEM_MESSAGE

def make_node(llm, prompt, state_key):
    def node(state: Dict[str, Any]) -> Dict[str, Any]:
        print(f"LLM node {state_key} starting - received state with keys: {list(state.keys())}")

        blob_tmp_path = None
        tool_env = None
        if 'markdown' in state and isinstance(state['markdown'], str):
            # Persist big input to temp file
            tf = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')
            tf.write(state['markdown'])
            tf.flush()
            tf.close()
            blob_tmp_path = tf.name
            tool_env = {"PY_TOOL_BLOB_PATH": blob_tmp_path}
            print(f"Node {state_key} - wrote large input to {blob_tmp_path}")

        try:
            filled = prompt.format(**state)
            print(f"Node {state_key} - prompt filled, length: {len(filled)} characters")
#            print(f"prompt: {filled[0:200]}")
        except KeyError as e:
            print(f"Missing key in state: {e}")
            print(f"Available keys: {list(state.keys())}")
            raise

        # Bind tools to the LLM
        llm_with_tools = llm.bind_tools([PYTHON_EXECUTOR_TOOL])


        messages = [
            SystemMessage(content=SYSTEM_MESSAGE),
            HumanMessage(content=filled)
        ]

        try:
            print(f"Node {state_key} - invoking LLM with tools...")
            response = llm_with_tools.invoke(messages)

            print(f"response: {response}")

            # Handle tool calls if present
            max_tool_iterations = 3  # Prevent infinite loops
            tool_iteration = 0

            while response.tool_calls and tool_iteration < max_tool_iterations:
                tool_iteration += 1
                print(f"Node {state_key} - processing {len(response.tool_calls)} tool calls (iteration {tool_iteration})")

                # Add the assistant message with tool calls first
                messages.append(response)

                # Then add all tool responses
                for tool_call in response.tool_calls:
                    if tool_call["name"] == "execute_python_code":
                        CODE_SPELLINGS = ("code", "code=")

                        args = tool_call.get("args", {})  # assume dict, keep it simple

                        code = None
                        for k in CODE_SPELLINGS:
                            if k in args and isinstance(args[k], str):
                                code = args[k]
                                break

                        if code is None:
                            print(f"[execute_python_code] Missing code arg. Available keys: {list(args.keys())}")
                            messages.append(ToolMessage(
                                content=json.dumps({
                                    "output": "",
                                    "error": "Missing 'code' in tool args",
                                    "success": False,
                                    "variables": {}
                                }),
                                tool_call_id=tool_call["id"]
                            ))
                            continue


                        #print(f"Node {state_key} - executing Python code")

                        # Execute the Python code
                        execution_result = execute_python_code(code, env=tool_env)

                        # Add tool result to messages
                        messages.append(ToolMessage(
                            content=json.dumps(execution_result),
                            tool_call_id=tool_call["id"]
                        ))

                # Continue conversation with tool results
                response = llm_with_tools.invoke(messages)

            # If we hit the max iterations, log a warning
            if response.tool_calls and tool_iteration >= max_tool_iterations:
                print(f"Warning: Node {state_key} hit maximum tool iterations ({max_tool_iterations}). Proceeding with current response.")

            final_content = response.content
            #print(f"Node {state_key} - received final response, length: {len(str(final_content))} characters")

        except Exception as e:
            print(f"Error invoking LLM for {state_key}: {str(e)}")
            print(f"State: {state}")
            if "timeout" in str(e).lower() or "timed out" in str(e).lower():
                print(f"Timeout occurred for {state_key}. Consider reducing input size or increasing timeout.")
            raise Exception(f"LLM invocation failed for {state_key}: {str(e)}")
        finally:
            # (B) Clean the temp file if we created one
            if blob_tmp_path:
                try:
                    os.unlink(blob_tmp_path)
                    print(f"Node {state_key} - cleaned up temp file {blob_tmp_path}")
                except Exception as _:
                    pass


        try:
            parsed = json.loads(final_content)
            final_response = parsed
            #print(f"Node {state_key} - successfully parsed JSON response")
        except Exception:
            #print(f"Node {state_key} - response is not JSON, using as-is")
            final_response = final_content

        #print(f"Node {state_key} - completed successfully")

        # Update state with python_context if it was used
        result = {state_key: final_response}
        if 'python_context' not in state:
            result['python_context'] = {}

        return result

    return node
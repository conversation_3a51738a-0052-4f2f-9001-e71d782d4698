

from typing import List, Dict, Type, TypedDict, Any
import json
from langgraph.graph import StateGraph, END
from langchain_openai import ChatOpenAI
from langchain.schema import SystemMessage, HumanMessage
from langchain_core.messages import ToolMessage
import os
from src.python_executor2 import execute_python_code, PYTHON_EXECUTOR_TOOL
import tempfile


def make_python_node(code_template, state_key):
    def node(state: Dict[str, Any]) -> Dict[str, Any]:
        print(f"Python node starting - received state with keys: {list(state.keys())}")        # Handle large markdown like in LLM node
        blob_tmp_path = None
        tool_env = None
        if 'markdown' in state and isinstance(state['markdown'], str):
            # Create temp file for large input
            tf = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8')
            tf.write(state['markdown'])
            tf.flush()
            tf.close()
            blob_tmp_path = tf.name
            tool_env = {"PY_TOOL_BLOB_PATH": blob_tmp_path}

        try:
            # flat_state = dict(state)
            # for key, value in state.items():
            #     if isinstance(value, dict):
            #         for subkey, subvalue in value.items():
            #             flat_state[f"{key}[{subkey}]"] = subvalue

            #print(flat_state)
            filled_code = code_template.format(**state)

            execution_result = execute_python_code(filled_code, env=tool_env)
            return {state_key: execution_result['variables']}
        finally:
            # Clean up temp file
            if blob_tmp_path:
                try:
                    os.unlink(blob_tmp_path)
                except:
                    pass

    return node
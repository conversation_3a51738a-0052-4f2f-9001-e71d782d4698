from langgraph.graph import StateGraph, END
from langchain_openai import ChatOpenAI
import os
from src.nodes.llm_node import make_node as make_llm_node
from src.nodes.python_node import make_python_node




def make_node(llm, prompt, state_key, step_type="llm"):
    if step_type == "python":
        return make_python_node(prompt, state_key)
    else:
        return make_llm_node(llm, prompt, state_key)

class LangGraphUtils:
    def __init__(self, model="gpt-4o-mini"):
        if model.lower().startswith("gpt"):
            self.llm = ChatOpenAI(model=model, #temperature=0.0,
                                  api_key=os.getenv("OPEN_AI_API_KEY"),
                                  timeout=60, max_retries=2) #, max_tokens=125_000)


    def build_graph(self, prompts, workflow):
        """Build a LangGraph from a list of prompts"""

        # Create a state graph with our dynamic state type
        sg = StateGraph(workflow)

        # Create a node for each prompt
        for step in prompts:
            step_name = step['name']
            step_type = step.get('type', 'llm')
            prompt_template = step['prompt']
            node_name = f"{step_name}_node"

            sg.add_node(node_name, make_node(self.llm, prompt_template, step_name, step_type))

        # Create edges between nodes
        for i in range(len(prompts) - 1):
            current_node = f"{prompts[i]['name']}_node"
            next_node = f"{prompts[i + 1]['name']}_node"
            sg.add_edge(current_node, next_node)

        # Set entry point and final edge
        first_node = f"{prompts[0]['name']}_node"
        last_node = f"{prompts[-1]['name']}_node"
        sg.add_edge(last_node, END)
        sg.set_entry_point(first_node)

        # Compile the graph
        return sg.compile()



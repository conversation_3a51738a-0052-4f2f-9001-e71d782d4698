DATASET_NAME = "csv_location"

def getFileNameWithoutExtension(String filename) {
    return filename.lastIndexOf('.') != -1 ? filename[0..filename.lastIndexOf('.') - 1] : filename
}

def encloseWithQuotes(x) {
    return "\"${x}\""
}

def prepareDataset() {
    try {
        unstash DATASET_NAME
        env.INPUT_FILEPATH = "./input/${csv_location_FILENAME}"
        def directoryPath = env.INPUT_FILEPATH.substring(0, env.INPUT_FILEPATH.lastIndexOf('/'))
        sh """
          mkdir -p ${directoryPath}
          mv ${DATASET_NAME} ${env.INPUT_FILEPATH}
        """
        echo "Prepared CSV at: ${env.INPUT_FILEPATH}"
    } catch (groovy.lang.MissingPropertyException e) {
        error 'You forgot to provide dataset file.'
    }
}

// --- Removed Active Choices listbox ---

pipeline {
  agent {
    node {
      label 'built-in'
    }
  }

  options {
    skipDefaultCheckout(true)
  }

  environment {
    PIP_DISABLE_PIP_VERSION_CHECK = '1'
    PYBIN = 'python3.11'
    VENV_DIR = "/var/jenkins_home/workspace/venvs/${JOB_NAME}/venv"
  }

  parameters {
    // NEW: simple text box for prompt files (comma-separated)
    string(
      name: 'PROMPT_FILES',
      defaultValue: 'client_summary.yaml, service_summary.yaml, imprint_contacts.yaml, employee_count.yaml, number_of_employees.yaml, founding_year.yaml, test.yaml',
      description: 'Comma-separated list of prompt files to run (e.g., "client_summary.yaml, service_summary.yaml").'
    )

    stashedFile(
      name: 'csv_location',
      description: 'Optional CSV with header column "url". Used when "urls" is empty (and not "DB").'
    )
    string(
      name: 'urls',
      defaultValue: 'acconsis.de,aktax.de,flick-gmbh.de,fm-steuer.de,forvismazars.com',
      description: 'Comma-separated URLs for RAG. Use "DB" to get from DB. Leave empty to use uploaded CSV.'
    )
    string(
      name: 'model',
      defaultValue: 'gpt-5-nano-2025-08-07',
      description: 'Which OpenAI model to use'
    )
    string(
      name: 'md_max_length',
      defaultValue: '350_000',
      description: 'First N characters of the markdown to use'
    )
    booleanParam(name: 'reinstall_requirements', defaultValue: false,
      description: 'If checked, (re)create venv and install requirements')
  }

  stages {
    stage('Checkout'){ steps { checkout scm } }

    stage('Prepare parameters / CSV (only if needed)') {
      steps {
        script {
            // normalize and decide if we should use CSV
            def urlsTrimmed = (params.urls ?: '').trim()
            def useDB = (urlsTrimmed == 'DB')
            def useInlineUrls = (!useDB && urlsTrimmed)
            def needCsv = (!useDB && !useInlineUrls)

            if (needCsv) {
              echo "urls is empty (and not 'DB') → expecting uploaded CSV (${DATASET_NAME})"
              prepareDataset() // will unstash and move to ./input/<original_filename>
            } else {
              echo useDB
                ? "Using DB for URLs."
                : "Using inline URLs parameter."
            }
          }
      }
    }

    stage('Prepare venv') {
      steps {
        sh '''
          set -eux
          if [ "${reinstall_requirements}" = "true" ]; then
            echo ">> Reinstall requested — recreating ${VENV_DIR}"
            rm -rf "${VENV_DIR}"
          fi

          if [ ! -d "${VENV_DIR}" ]; then
            echo ">> Creating venv at ${VENV_DIR}"
            ${PYBIN} -m venv "${VENV_DIR}"
            . "${VENV_DIR}/bin/activate"
            # python -m pip install --upgrade pip
            pip install --no-cache-dir -r requirements.txt
          else
            echo ">> Reusing existing venv at ${VENV_DIR}"
            . "${VENV_DIR}/bin/activate"
            python -V
            pip -V
          fi
        '''
      }
    }

    stage('Run selected prompts in parallel') {
      when {
        // works for a String parameter (non-empty check)
        expression { return params.PROMPT_FILES && params.PROMPT_FILES.size() > 0 }
      }
      steps {
        script {
          // Normalize to List<String> by splitting comma-separated text
          def selected = params.PROMPT_FILES
          if (selected instanceof String) {
            selected = selected.split(',').collect { it.trim() }.findAll { it }
          }

          def branches = [:]
          selected.each { promptFile ->
            branches["${promptFile}"] = {
              sh """
                set -e
                . "$VENV_DIR/bin/activate"

                URLS_TRIMMED=\$(printf %s "$urls" | sed 's/^\\s*//;s/\\s*\$//')

                if [ "\$URLS_TRIMMED" = "DB" ]; then
                  TARGET_FLAG="--urls"; TARGET_VAL="\$URLS_TRIMMED"
                  echo ">> Using DB for URLs"
                elif [ -n "\$URLS_TRIMMED" ]; then
                  TARGET_FLAG="--urls"; TARGET_VAL="\$URLS_TRIMMED"
                  echo ">> Using URLs from parameter"
                else
                  if [ -n "\$INPUT_FILEPATH" ] && [ -f "\$INPUT_FILEPATH" ]; then
                    TARGET_FLAG="--csv_file"; TARGET_VAL="\$INPUT_FILEPATH"
                    echo ">> Using uploaded CSV: \$INPUT_FILEPATH"
                  else
                    echo "ERROR: urls is empty and no CSV was provided."
                    exit 1
                  fi
                fi

                echo ">> Running prompt: ${promptFile}"
                python -u -m src.driver \\
                  "\$TARGET_FLAG" "\$TARGET_VAL" \\
                  --prompt_yaml_filename "${promptFile}" \\
                  --model "$model" \\
                  --md_max_length "$md_max_length"
              """
            }
          }

          parallel branches
        }
      }
    }
  }

}

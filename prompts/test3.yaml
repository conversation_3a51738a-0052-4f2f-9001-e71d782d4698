metadata:
  owner: "<PERSON>i"
  markdown_db_table: "wget2_runs"

prompts:
  - name: test_step1
    type: "llm"
    prompt: |
      You are given markdown of a company website section.
  
      input: {markdown}
  
      From it extract the company name.
      Use Python (execute_python_code) to count the length of the characters in the company name.
      Make sure to assign the result to a variable so it can be captured.
      
      Example code:
      ```python
      text = """extracted company name"""
      length = len(text)
      print("Character count: %text%")
      ```
      
      Output: a JSON with the value of the variable "length" and the "company_name" that you extracted.
      {{
        "length": ... // the length of the company name from executing the Python code
        "company_name": ... // the extracted company name from your reasoning
      }}
        But don't preface it with ```json.

  - name: test_step2
    type: "python"
    prompt: |
      company_name = "{test_step1[company_name]}"
      
      # Do Python processing
      length = len(company_name)
      reversed_name = company_name[::-1].upper()
      
      print(f"Company: {company_name}")
      print(f"Length: {length}")
      print(f"Reversed: {reversed_name}")
